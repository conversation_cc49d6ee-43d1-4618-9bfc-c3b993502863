# 2FAuth 环境变量设置脚本
# 使用方法: .\setup-secrets.ps1

Write-Host "🔐 设置 2FAuth 环境变量..." -ForegroundColor Green

# 生成随机密钥的函数
function Generate-RandomKey {
    param([int]$Length = 32)
    $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    $key = ""
    for ($i = 0; $i -lt $Length; $i++) {
        $key += $chars[(Get-Random -Maximum $chars.Length)]
    }
    return $key
}

Write-Host "请按照提示输入或生成以下环境变量:" -ForegroundColor Yellow

# OAuth 客户端 ID
Write-Host "`n1. OAuth 客户端 ID (OAUTH_CLIENT_ID):" -ForegroundColor Cyan
$oauthClientId = Read-Host "请输入 OAuth 客户端 ID"
if ($oauthClientId) {
    Write-Host "设置 OAUTH_CLIENT_ID..." -ForegroundColor Yellow
    echo $oauthClientId | wrangler secret put OAUTH_CLIENT_ID
}

# OAuth 客户端密钥
Write-Host "`n2. OAuth 客户端密钥 (OAUTH_CLIENT_SECRET):" -ForegroundColor Cyan
$oauthClientSecret = Read-Host "请输入 OAuth 客户端密钥" -AsSecureString
if ($oauthClientSecret) {
    $oauthClientSecretPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($oauthClientSecret))
    Write-Host "设置 OAUTH_CLIENT_SECRET..." -ForegroundColor Yellow
    echo $oauthClientSecretPlain | wrangler secret put OAUTH_CLIENT_SECRET
}

# JWT 密钥
Write-Host "`n3. JWT 密钥 (JWT_SECRET):" -ForegroundColor Cyan
$choice = Read-Host "是否自动生成 JWT 密钥? (y/n)"
if ($choice -eq "y" -or $choice -eq "Y") {
    $jwtSecret = Generate-RandomKey -Length 64
    Write-Host "生成的 JWT 密钥: $jwtSecret" -ForegroundColor Green
    Write-Host "设置 JWT_SECRET..." -ForegroundColor Yellow
    echo $jwtSecret | wrangler secret put JWT_SECRET
} else {
    $jwtSecret = Read-Host "请输入 JWT 密钥"
    if ($jwtSecret) {
        Write-Host "设置 JWT_SECRET..." -ForegroundColor Yellow
        echo $jwtSecret | wrangler secret put JWT_SECRET
    }
}

# 加密密钥
Write-Host "`n4. 加密密钥 (ENCRYPTION_KEY):" -ForegroundColor Cyan
$choice = Read-Host "是否自动生成加密密钥? (y/n)"
if ($choice -eq "y" -or $choice -eq "Y") {
    $encryptionKey = Generate-RandomKey -Length 32
    Write-Host "生成的加密密钥: $encryptionKey" -ForegroundColor Green
    Write-Host "设置 ENCRYPTION_KEY..." -ForegroundColor Yellow
    echo $encryptionKey | wrangler secret put ENCRYPTION_KEY
} else {
    $encryptionKey = Read-Host "请输入加密密钥"
    if ($encryptionKey) {
        Write-Host "设置 ENCRYPTION_KEY..." -ForegroundColor Yellow
        echo $encryptionKey | wrangler secret put ENCRYPTION_KEY
    }
}

Write-Host "`n✅ 环境变量设置完成！" -ForegroundColor Green
Write-Host "可以运行 .\deploy.ps1 进行部署" -ForegroundColor Cyan
