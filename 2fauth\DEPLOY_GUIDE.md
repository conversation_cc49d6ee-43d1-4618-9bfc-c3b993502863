# 2FAuth Cloudflare Workers 部署指南

## 📋 部署前准备

### 1. 环境要求
- [Node.js](https://nodejs.org/) (推荐 v18+)
- [Cloudflare 账号](https://dash.cloudflare.com/)
- OAuth 2.0 认证服务器（如 GitHub、GitLab、自建等）

### 2. 安装 Wrangler CLI
```bash
npm install -g wrangler
```

### 3. 登录 Cloudflare
```bash
wrangler login
```

## 🚀 快速部署

### 方法一：使用自动化脚本（推荐）

1. **设置环境变量**
   ```powershell
   .\setup-secrets.ps1
   ```

2. **部署应用**
   ```powershell
   .\deploy.ps1
   ```

### 方法二：手动部署

#### 步骤 1：创建 KV 命名空间
```bash
# 创建生产环境 KV 命名空间
wrangler kv:namespace create "USER_DATA"

# 创建预览环境 KV 命名空间（可选）
wrangler kv:namespace create "USER_DATA" --preview
```

#### 步骤 2：配置环境变量
```bash
# OAuth 配置
wrangler secret put OAUTH_CLIENT_ID
wrangler secret put OAUTH_CLIENT_SECRET

# 安全密钥
wrangler secret put JWT_SECRET
wrangler secret put ENCRYPTION_KEY
```

#### 步骤 3：更新 wrangler.toml
确保 `wrangler.toml` 中的 KV 命名空间 ID 正确：
```toml
[[kv_namespaces]]
binding = "USER_DATA"
id = "your-kv-namespace-id"  # 替换为实际的 ID
preview_id = "your-preview-kv-namespace-id"  # 可选
```

#### 步骤 4：部署
```bash
wrangler deploy
```

## ⚙️ 配置说明

### OAuth 配置
在 `wrangler.toml` 中配置 OAuth 相关参数：

```toml
[vars]
OAUTH_BASE_URL = "https://your-oauth-server.com"
OAUTH_REDIRECT_URI = "https://your-domain.workers.dev/api/oauth/callback"
OAUTH_ID = "authorized_user_id"

[env.production.vars]
ALLOWED_ORIGINS = "https://your-domain.workers.dev"
```

### 环境变量说明

| 变量名 | 说明 | 示例 |
|--------|------|------|
| `OAUTH_CLIENT_ID` | OAuth 客户端 ID | `your_client_id` |
| `OAUTH_CLIENT_SECRET` | OAuth 客户端密钥 | `your_client_secret` |
| `JWT_SECRET` | JWT 签名密钥 | 64位随机字符串 |
| `ENCRYPTION_KEY` | 数据加密密钥 | 32位随机字符串 |

## 🔧 故障排除

### 常见问题

1. **KV 命名空间错误**
   - 确保 `wrangler.toml` 中的 KV 命名空间 ID 正确
   - 检查绑定名称是否为 `USER_DATA`

2. **OAuth 认证失败**
   - 检查 OAuth 客户端 ID 和密钥是否正确
   - 确认回调 URL 配置正确
   - 验证授权用户 ID 是否匹配

3. **部署失败**
   - 检查 Wrangler 是否已登录：`wrangler whoami`
   - 确认所有必需的环境变量已设置
   - 查看详细错误信息：`wrangler deploy --verbose`

### 调试命令

```bash
# 查看部署状态
wrangler deployments list

# 查看实时日志
wrangler tail

# 测试本地开发
wrangler dev

# 查看环境变量
wrangler secret list
```

## 🔒 安全建议

1. **强密钥策略**
   - JWT_SECRET 至少 64 个字符
   - ENCRYPTION_KEY 至少 32 个字符
   - 使用强随机字符串

2. **OAuth 安全**
   - 使用 HTTPS 回调 URL
   - 定期轮换客户端密钥
   - 限制授权用户范围

3. **生产环境**
   - 设置正确的 ALLOWED_ORIGINS
   - 启用 Cloudflare 安全功能
   - 定期备份 KV 数据

## 📱 使用说明

部署成功后，访问您的 Workers 域名即可使用 2FA 管理系统：

1. **OAuth 登录** - 使用配置的 OAuth 服务登录
2. **添加账户** - 手动输入或扫描二维码添加 2FA 账户
3. **生成验证码** - 点击账户卡片生成 TOTP 验证码
4. **WebDAV 备份** - 配置 WebDAV 服务进行数据备份
5. **数据导入导出** - 支持多种格式的数据迁移

## 🆘 获取帮助

如果遇到问题，请：
1. 查看 [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
2. 检查项目 README.md 中的详细说明
3. 查看 GitHub Issues 或提交新问题
