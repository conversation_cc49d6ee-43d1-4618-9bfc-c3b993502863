# 2FAuth 部署状态检查脚本
# 使用方法: .\check-deployment.ps1

Write-Host "🔍 检查 2FAuth 部署状态..." -ForegroundColor Green

# 检查 wrangler 安装
Write-Host "`n1. 检查 Wrangler CLI..." -ForegroundColor Cyan
try {
    $version = wrangler --version
    Write-Host "✅ Wrangler 已安装: $version" -ForegroundColor Green
} catch {
    Write-Host "❌ Wrangler 未安装" -ForegroundColor Red
    Write-Host "请运行: npm install -g wrangler" -ForegroundColor Yellow
    exit 1
}

# 检查登录状态
Write-Host "`n2. 检查 Cloudflare 登录状态..." -ForegroundColor Cyan
try {
    $whoami = wrangler whoami
    Write-Host "✅ 已登录 Cloudflare" -ForegroundColor Green
    Write-Host $whoami -ForegroundColor Gray
} catch {
    Write-Host "❌ 未登录 Cloudflare" -ForegroundColor Red
    Write-Host "请运行: wrangler login" -ForegroundColor Yellow
    exit 1
}

# 检查配置文件
Write-Host "`n3. 检查配置文件..." -ForegroundColor Cyan
if (Test-Path "wrangler.toml") {
    Write-Host "✅ wrangler.toml 存在" -ForegroundColor Green
    
    # 读取配置内容
    $config = Get-Content "wrangler.toml" -Raw
    if ($config -match 'main\s*=\s*"_worker\.js"') {
        Write-Host "✅ main 文件路径正确" -ForegroundColor Green
    } else {
        Write-Host "⚠️  main 文件路径可能不正确" -ForegroundColor Yellow
    }
    
    if ($config -match 'binding\s*=\s*"USER_DATA"') {
        Write-Host "✅ KV 绑定名称正确" -ForegroundColor Green
    } else {
        Write-Host "⚠️  KV 绑定名称可能不正确" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ wrangler.toml 不存在" -ForegroundColor Red
    exit 1
}

# 检查主文件
Write-Host "`n4. 检查主程序文件..." -ForegroundColor Cyan
if (Test-Path "_worker.js") {
    Write-Host "✅ _worker.js 存在" -ForegroundColor Green
    $fileSize = (Get-Item "_worker.js").Length
    Write-Host "文件大小: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Gray
} else {
    Write-Host "❌ _worker.js 不存在" -ForegroundColor Red
    exit 1
}

# 检查环境变量
Write-Host "`n5. 检查环境变量..." -ForegroundColor Cyan
$requiredSecrets = @("OAUTH_CLIENT_ID", "OAUTH_CLIENT_SECRET", "JWT_SECRET", "ENCRYPTION_KEY")
$missingSecrets = @()

foreach ($secret in $requiredSecrets) {
    try {
        $result = wrangler secret list 2>$null | Select-String $secret
        if ($result) {
            Write-Host "✅ $secret 已配置" -ForegroundColor Green
        } else {
            Write-Host "❌ $secret 未配置" -ForegroundColor Red
            $missingSecrets += $secret
        }
    } catch {
        Write-Host "⚠️  无法检查 $secret" -ForegroundColor Yellow
        $missingSecrets += $secret
    }
}

# 检查 KV 命名空间
Write-Host "`n6. 检查 KV 命名空间..." -ForegroundColor Cyan
try {
    $kvList = wrangler kv:namespace list 2>$null
    if ($kvList) {
        Write-Host "✅ KV 命名空间可访问" -ForegroundColor Green
    } else {
        Write-Host "⚠️  无法获取 KV 命名空间列表" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ KV 命名空间检查失败" -ForegroundColor Red
}

# 总结
Write-Host "`n📊 检查总结:" -ForegroundColor Green
if ($missingSecrets.Count -eq 0) {
    Write-Host "✅ 所有检查通过，可以进行部署！" -ForegroundColor Green
    Write-Host "运行 .\deploy.ps1 开始部署" -ForegroundColor Cyan
} else {
    Write-Host "⚠️  发现问题，需要解决以下环境变量:" -ForegroundColor Yellow
    foreach ($secret in $missingSecrets) {
        Write-Host "  - $secret" -ForegroundColor Red
    }
    Write-Host "运行 .\setup-secrets.ps1 设置环境变量" -ForegroundColor Cyan
}

Write-Host "`n🔗 有用的命令:" -ForegroundColor Blue
Write-Host "  wrangler dev          # 本地开发测试" -ForegroundColor Gray
Write-Host "  wrangler tail         # 查看实时日志" -ForegroundColor Gray
Write-Host "  wrangler deployments  # 查看部署历史" -ForegroundColor Gray
