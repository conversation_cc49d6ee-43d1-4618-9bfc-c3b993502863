# 2FAuth Cloudflare Workers 部署脚本
# 使用方法: .\deploy.ps1

Write-Host "🚀 开始部署 2FAuth 到 Cloudflare Workers..." -ForegroundColor Green

# 检查 wrangler 是否已安装
try {
    $wranglerVersion = wrangler --version
    Write-Host "✅ Wrangler 已安装: $wranglerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Wrangler 未安装，正在安装..." -ForegroundColor Red
    npm install -g wrangler
}

# 检查是否已登录
Write-Host "🔐 检查 Cloudflare 登录状态..." -ForegroundColor Yellow
try {
    wrangler whoami
} catch {
    Write-Host "请先登录 Cloudflare:" -ForegroundColor Yellow
    wrangler login
}

# 检查必要的环境变量
Write-Host "🔧 检查环境变量配置..." -ForegroundColor Yellow
$requiredSecrets = @("OAUTH_CLIENT_ID", "OAUTH_CLIENT_SECRET", "JWT_SECRET", "ENCRYPTION_KEY")

foreach ($secret in $requiredSecrets) {
    Write-Host "检查 $secret..." -ForegroundColor Cyan
    try {
        $result = wrangler secret list | Select-String $secret
        if ($result) {
            Write-Host "✅ $secret 已配置" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $secret 未配置，请手动设置:" -ForegroundColor Yellow
            Write-Host "wrangler secret put $secret" -ForegroundColor Cyan
        }
    } catch {
        Write-Host "⚠️  无法检查 $secret 状态" -ForegroundColor Yellow
    }
}

# 验证 KV 命名空间
Write-Host "📦 验证 KV 命名空间..." -ForegroundColor Yellow
try {
    wrangler kv:namespace list
    Write-Host "✅ KV 命名空间列表已获取" -ForegroundColor Green
} catch {
    Write-Host "❌ 无法获取 KV 命名空间列表" -ForegroundColor Red
}

# 部署到 Cloudflare Workers
Write-Host "🚀 开始部署..." -ForegroundColor Green
try {
    wrangler deploy
    Write-Host "✅ 部署成功！" -ForegroundColor Green
    Write-Host "🌐 访问地址: https://2fa.gally16.workers.dev" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 部署失败，请检查配置" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 部署完成！" -ForegroundColor Green
